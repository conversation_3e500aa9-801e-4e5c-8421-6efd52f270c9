<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fleet Build Options - Admiral Lounge</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="neon-grid">
        <!-- Animated neon lines background -->
        <div class="neon-line horizontal line-1"></div>
        <div class="neon-line horizontal line-2"></div>
        <div class="neon-line vertical line-3"></div>
        <div class="neon-line vertical line-4"></div>
        <div class="neon-line diagonal line-5"></div>
        <div class="neon-line diagonal line-6"></div>
    </div>

    <header class="main-header">
        <div class="header-content">
            <h1 class="site-title">Admiral Lounge</h1>
            <nav class="main-nav">
                <ul>
                    <li><a href="Index.html">Home</a></li>
                    <li><a href="core-info.html">Core Info</a></li>
                    <li><a href="fleet-builds.html">Fleet Builds</a></li>
                    <li><a href="advanced-tips.html">Advanced Tips</a></li>
                    <li><a href="tactical-warfare.html">Tactical Warfare</a></li>
                </ul>
            </nav>
        </div>
        <div class="header-border"></div>
    </header>

    <main class="main-content">
        <section class="content-page">
            <div class="page-header">
                <h1 class="page-title">Fleet Build Options</h1>
                <p class="page-description">Play around with fleet build ideas and plan ahead for your future</p>

                <!-- Fleet Options -->
                <div class="fleet-options">
                    <button class="fleet-option active" onclick="selectFleet(1)" data-fleet="1">Fleet 1</button>
                    <button class="fleet-option" onclick="selectFleet(2)" data-fleet="2">Fleet 2</button>
                    <button class="fleet-option" onclick="selectFleet(3)" data-fleet="3">Fleet 3</button>
                    <button class="fleet-option" onclick="selectFleet(4)" data-fleet="4">Fleet 4</button>
                    <button class="fleet-option" onclick="selectFleet(5)" data-fleet="5">Fleet 5</button>
                    <button class="fleet-option" onclick="selectFleet(6)" data-fleet="6">Fleet 6</button>
                </div>

                <!-- Admiral Selection Display -->
                <div id="selected-admiral" class="selected-admiral" style="display: none;">
                    <img id="admiral-image" src="" alt="Selected Admiral" class="admiral-image">
                </div>
            </div>

            <!-- Fleet Content Sections -->
            <div class="fleet-content">
                <div id="fleet-1" class="fleet-section active">
                    <button class="choose-admiral-btn" onclick="openAdmiralModal()">Choose Admiral</button>
                </div>

                <div id="fleet-2" class="fleet-section">
                    <button class="choose-admiral-btn" onclick="openAdmiralModal()">Choose Admiral</button>
                </div>

                <div id="fleet-3" class="fleet-section">
                    <button class="choose-admiral-btn" onclick="openAdmiralModal()">Choose Admiral</button>
                </div>

                <div id="fleet-4" class="fleet-section">
                    <button class="choose-admiral-btn" onclick="openAdmiralModal()">Choose Admiral</button>
                </div>

                <div id="fleet-5" class="fleet-section">
                    <button class="choose-admiral-btn" onclick="openAdmiralModal()">Choose Admiral</button>
                </div>

                <div id="fleet-6" class="fleet-section">
                    <button class="choose-admiral-btn" onclick="openAdmiralModal()">Choose Admiral</button>
                </div>
            </div>
        </section>

        <!-- Admiral Selection Modal -->
        <div id="admiral-modal" class="admiral-modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Choose Admiral</h3>
                    <button class="close-modal" onclick="closeAdmiralModal()">&times;</button>
                </div>
                <div class="admiral-grid">
                    <div class="admiral-option" onclick="selectAdmiral('admiral1.jpg', 'Admiral Vega')">
                        <div class="admiral-placeholder">Admiral 1</div>
                    </div>
                    <div class="admiral-option" onclick="selectAdmiral('admiral2.jpg', 'Admiral Nova')">
                        <div class="admiral-placeholder">Admiral 2</div>
                    </div>
                    <div class="admiral-option" onclick="selectAdmiral('admiral3.jpg', 'Admiral Orion')">
                        <div class="admiral-placeholder">Admiral 3</div>
                    </div>
                    <div class="admiral-option" onclick="selectAdmiral('admiral4.jpg', 'Admiral Stellar')">
                        <div class="admiral-placeholder">Admiral 4</div>
                    </div>
                    <div class="admiral-option" onclick="selectAdmiral('admiral5.jpg', 'Admiral Cosmos')">
                        <div class="admiral-placeholder">Admiral 5</div>
                    </div>
                    <div class="admiral-option" onclick="selectAdmiral('admiral6.jpg', 'Admiral Galaxy')">
                        <div class="admiral-placeholder">Admiral 6</div>
                    </div>
                    <div class="admiral-option" onclick="selectAdmiral('admiral7.jpg', 'Admiral Nebula')">
                        <div class="admiral-placeholder">Admiral 7</div>
                    </div>
                    <div class="admiral-option" onclick="selectAdmiral('admiral8.jpg', 'Admiral Quasar')">
                        <div class="admiral-placeholder">Admiral 8</div>
                    </div>
                    <div class="admiral-option" onclick="selectAdmiral('admiral9.jpg', 'Admiral Pulsar')">
                        <div class="admiral-placeholder">Admiral 9</div>
                    </div>
                    <div class="admiral-option" onclick="selectAdmiral('admiral10.jpg', 'Admiral Comet')">
                        <div class="admiral-placeholder">Admiral 10</div>
                    </div>
                    <div class="admiral-option" onclick="selectAdmiral('admiral11.jpg', 'Admiral Meteor')">
                        <div class="admiral-placeholder">Admiral 11</div>
                    </div>
                    <div class="admiral-option" onclick="selectAdmiral('admiral12.jpg', 'Admiral Astro')">
                        <div class="admiral-placeholder">Admiral 12</div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        let currentFleet = 1;
        let fleetAdmirals = {}; // Store admiral selections for each fleet

        function selectFleet(fleetNumber) {
            // Hide current fleet section
            document.getElementById(`fleet-${currentFleet}`).classList.remove('active');
            document.querySelector(`[data-fleet="${currentFleet}"]`).classList.remove('active');

            // Show selected fleet section
            document.getElementById(`fleet-${fleetNumber}`).classList.add('active');
            document.querySelector(`[data-fleet="${fleetNumber}"]`).classList.add('active');

            currentFleet = fleetNumber;

            // Update admiral display for the selected fleet
            updateAdmiralDisplay();
        }

        function updateAdmiralDisplay() {
            const admiralDisplay = document.getElementById('selected-admiral');
            const admiralImage = document.getElementById('admiral-image');

            if (fleetAdmirals[currentFleet]) {
                // Show the admiral for the current fleet
                admiralImage.src = fleetAdmirals[currentFleet].imagePath;
                admiralImage.alt = fleetAdmirals[currentFleet].name;
                admiralDisplay.style.display = 'block';
            } else {
                // Hide admiral display if no admiral selected for this fleet
                admiralDisplay.style.display = 'none';
            }
        }

        function openAdmiralModal() {
            document.getElementById('admiral-modal').style.display = 'flex';
            updateAdmiralAvailability();
        }

        function updateAdmiralAvailability() {
            // Get all taken admirals (excluding current fleet)
            const takenAdmirals = [];
            for (let fleet in fleetAdmirals) {
                if (fleet != currentFleet) {
                    takenAdmirals.push(fleetAdmirals[fleet].imagePath);
                }
            }

            // Update admiral options in the modal
            const admiralOptions = document.querySelectorAll('.admiral-option');
            admiralOptions.forEach((option, index) => {
                const admiralPath = `assets/images/admiral${index + 1}.jpg`;

                if (takenAdmirals.includes(admiralPath)) {
                    // Admiral is taken by another fleet
                    option.classList.add('taken');
                    option.onclick = null; // Disable clicking
                } else {
                    // Admiral is available
                    option.classList.remove('taken');
                    // Re-enable clicking with the original function
                    const admiralName = option.querySelector('.admiral-placeholder').textContent;
                    option.onclick = function() {
                        selectAdmiral(`admiral${index + 1}.jpg`, admiralName);
                    };
                }
            });
        }

        function closeAdmiralModal() {
            document.getElementById('admiral-modal').style.display = 'none';
        }

        function selectAdmiral(imagePath, admiralName) {
            // Store the admiral selection for the current fleet
            fleetAdmirals[currentFleet] = {
                imagePath: `assets/images/${imagePath}`,
                name: admiralName
            };

            // Update the display
            updateAdmiralDisplay();

            // Close the modal
            closeAdmiralModal();
        }

        // Close modal when clicking outside of it
        window.onclick = function(event) {
            const modal = document.getElementById('admiral-modal');
            if (event.target === modal) {
                closeAdmiralModal();
            }
        }

        // Initialize the display for fleet 1
        updateAdmiralDisplay();
    </script>
</body>
</html>
