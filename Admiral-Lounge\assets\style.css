/* Admiral Lounge - Sci-<PERSON> Neon Theme */

/* CSS Variables for Color Scheme */
:root {
    --primary-orange: #FF5F1F;
    --primary-purple: #BC13FE;
    --background-black: #000000;
    --text-white: #FFFFFF;
    --font-heading: '<PERSON><PERSON>', sans-serif;
    --font-body: 'Playfair Display', serif;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background-color: var(--background-black);
    color: var(--text-white);
    font-family: var(--font-body);
    line-height: 1.6;
    overflow-x: hidden;
    position: relative;
}

/* Neon Grid Background */
.neon-grid {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    pointer-events: none;
    z-index: 1;
}

.neon-line {
    position: absolute;
    background: linear-gradient(90deg,
        transparent 0%,
        var(--primary-orange) 20%,
        var(--primary-purple) 80%,
        transparent 100%);
    box-shadow:
        0 0 10px var(--primary-orange),
        0 0 20px var(--primary-purple),
        0 0 30px var(--primary-orange);
    animation: neonPulse 3s ease-in-out infinite alternate;
}

.neon-line.horizontal {
    width: 100vw;
    height: 2px;
}

.neon-line.vertical {
    width: 2px;
    height: 100vh;
}

.neon-line.diagonal {
    width: 2px;
    height: 141vh;
    transform-origin: top left;
}

/* Neon Line Positioning */
.line-1 {
    top: 20%;
    left: 0;
    animation-delay: 0s;
}

.line-2 {
    bottom: 30%;
    left: 0;
    animation-delay: 1s;
}

.line-3 {
    left: 15%;
    top: 0;
    animation-delay: 0.5s;
}

.line-4 {
    right: 25%;
    top: 0;
    animation-delay: 1.5s;
}

.line-5 {
    top: 0;
    left: 10%;
    transform: rotate(45deg);
    animation-delay: 2s;
}

.line-6 {
    top: 0;
    right: 20%;
    transform: rotate(-45deg);
    animation-delay: 2.5s;
}

/* Neon Animations */
@keyframes neonPulse {
    0% {
        opacity: 0.3;
        filter: brightness(0.8);
    }
    100% {
        opacity: 0.8;
        filter: brightness(1.2);
    }
}

/* Typography */
h1, h2 {
    font-family: var(--font-heading);
    font-weight: 400;
    letter-spacing: 2px;
    text-transform: uppercase;
}

h1 {
    font-size: clamp(1.8rem, 3.5vw, 2.5rem);
    text-shadow:
        0 0 10px var(--primary-orange),
        0 0 20px var(--primary-purple),
        0 0 30px var(--primary-orange);
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    text-shadow:
        0 0 5px var(--primary-orange),
        0 0 10px var(--primary-purple);
}

h3 {
    font-family: var(--font-heading);
    font-size: clamp(1.5rem, 3vw, 2rem);
    letter-spacing: 1px;
    margin-bottom: 1rem;
    color: var(--primary-orange);
}

p {
    font-size: clamp(1rem, 2vw, 1.2rem);
    margin-bottom: 1rem;
    font-weight: 400;
}

/* Layout Components */
.main-header {
    position: relative;
    z-index: 10;
    padding: 1rem 0;
    border-bottom: 1px solid var(--primary-purple);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.site-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 400;
    letter-spacing: 4px;
    text-transform: uppercase;
    color: var(--text-white);
    text-shadow:
        0 0 10px var(--primary-orange),
        0 0 20px var(--primary-purple),
        0 0 30px var(--primary-orange);
    animation: glowShift 3s ease-in-out infinite alternate;
    position: relative;
    z-index: 2;
}

@keyframes glowShift {
    0% {
        text-shadow:
            0 0 10px var(--primary-orange),
            0 0 20px var(--primary-orange),
            0 0 30px var(--primary-orange);
    }
    100% {
        text-shadow:
            0 0 10px var(--primary-purple),
            0 0 20px var(--primary-purple),
            0 0 30px var(--primary-purple);
    }
}

.main-nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.main-nav a {
    color: var(--text-white);
    text-decoration: none;
    font-family: var(--font-heading);
    font-size: 1.5rem;
    letter-spacing: 2px;
    text-transform: uppercase;
    position: relative;
    transition: all 0.3s ease;
}

.main-nav a:hover {
    color: var(--primary-orange);
    text-shadow: 0 0 10px var(--primary-orange);
}

.main-nav a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-orange), var(--primary-purple));
    transition: width 0.3s ease;
}

.main-nav a:hover::after {
    width: 100%;
}

.header-border {
    height: 2px;
    background: linear-gradient(90deg,
        transparent 0%,
        var(--primary-orange) 20%,
        var(--primary-purple) 80%,
        transparent 100%);
    box-shadow: 0 0 10px var(--primary-orange);
}

/* Main Content */
.main-content {
    position: relative;
    z-index: 5;
    padding: 1rem 0;
}

/* Hero Section */
.hero-section {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0.5rem 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    min-height: 60vh;
}

.hero-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.hero-title {
    margin-bottom: 1rem;
}

.hero-subtitle {
    color: var(--primary-purple);
    margin-bottom: 2rem;
}

.hero-description {
    font-size: 1.2rem;
    line-height: 1.8;
    margin-bottom: 3rem;
    opacity: 0.9;
}

/* CTA Buttons */
.cta-container {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.cta-button {
    padding: 1rem 2rem;
    font-family: var(--font-heading);
    font-size: 1.1rem;
    letter-spacing: 1px;
    text-transform: uppercase;
    border: 2px solid;
    background: transparent;
    color: var(--text-white);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.cta-button.primary {
    border-color: var(--primary-orange);
    color: var(--primary-orange);
}

.cta-button.secondary {
    border-color: var(--primary-purple);
    color: var(--primary-purple);
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    transition: left 0.3s ease;
    z-index: -1;
}

.cta-button.primary::before {
    background: var(--primary-orange);
}

.cta-button.secondary::before {
    background: var(--primary-purple);
}

.cta-button:hover::before {
    left: 0;
}

.cta-button:hover {
    color: var(--background-black);
    box-shadow: 0 0 20px currentColor;
}

/* Hero Visual */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.neon-frame {
    width: 300px;
    height: 300px;
    border: 2px solid var(--primary-orange);
    border-radius: 50%;
    position: relative;
    box-shadow:
        0 0 20px var(--primary-orange),
        inset 0 0 20px var(--primary-purple);
    animation: frameRotate 10s linear infinite;
}

.frame-content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.pulse-circle {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: radial-gradient(circle, var(--primary-purple), transparent);
    animation: pulse 2s ease-in-out infinite alternate;
}

@keyframes frameRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    100% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* Content Sections */
.content-section {
    max-width: 1200px;
    margin: 0 auto;
    padding: 4rem 2rem;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-divider {
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-orange), var(--primary-purple));
    margin: 2rem auto;
    box-shadow: 0 0 10px var(--primary-orange);
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
    margin-top: 3rem;
}

.content-card {
    padding: 2rem;
    border: 1px solid var(--primary-purple);
    background: rgba(188, 19, 254, 0.05);
    position: relative;
    transition: all 0.3s ease;
}

.content-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-orange), var(--primary-purple));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.content-card:hover::before {
    transform: scaleX(1);
}

.content-card:hover {
    border-color: var(--primary-orange);
    box-shadow: 0 0 20px rgba(255, 95, 31, 0.3);
    transform: translateY(-5px);
}

/* Services Grid */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
    margin-top: 3rem;
}

.service-item {
    text-align: center;
    padding: 3rem 2rem;
    border: 1px solid var(--primary-orange);
    background: rgba(255, 95, 31, 0.05);
    position: relative;
    transition: all 0.3s ease;
}

.service-item:hover {
    border-color: var(--primary-purple);
    box-shadow: 0 0 30px rgba(188, 19, 254, 0.3);
    transform: translateY(-10px);
}

.service-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 2rem;
    border: 2px solid var(--primary-purple);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.icon-glow {
    width: 40px;
    height: 40px;
    background: radial-gradient(circle, var(--primary-orange), transparent);
    border-radius: 50%;
    animation: iconPulse 2s ease-in-out infinite alternate;
}

@keyframes iconPulse {
    0% {
        transform: scale(0.8);
        opacity: 0.6;
    }
    100% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* Footer */
.main-footer {
    position: relative;
    z-index: 10;
    margin-top: 6rem;
    border-top: 1px solid var(--primary-orange);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 4rem 2rem 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
}

.footer-section h3 {
    color: var(--primary-purple);
    margin-bottom: 1.5rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: var(--text-white);
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-section a:hover {
    color: var(--primary-orange);
    text-shadow: 0 0 5px var(--primary-orange);
}

.footer-border {
    height: 2px;
    background: linear-gradient(90deg,
        transparent 0%,
        var(--primary-purple) 20%,
        var(--primary-orange) 80%,
        transparent 100%);
    box-shadow: 0 0 10px var(--primary-purple);
}

.footer-bottom {
    text-align: center;
    padding: 2rem;
    opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .header-content {
        flex-direction: column;
        gap: 2rem;
    }

    .main-nav ul {
        gap: 1rem;
    }

    .main-nav a {
        font-size: 1.2rem;
    }

    .cta-container {
        justify-content: center;
    }

    .neon-frame {
        width: 200px;
        height: 200px;
    }

    .pulse-circle {
        width: 100px;
        height: 100px;
    }

    .content-grid {
        grid-template-columns: 1fr;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    /* Adjust neon lines for mobile */
    .line-5, .line-6 {
        display: none;
    }
}

@media (max-width: 480px) {
    .hero-section {
        padding: 2rem 1rem;
    }

    .content-section {
        padding: 2rem 1rem;
    }

    .header-content {
        padding: 0 1rem;
    }

    .cta-button {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }

    .service-item {
        padding: 2rem 1rem;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Selection Styling */
::selection {
    background: var(--primary-orange);
    color: var(--background-black);
}

::-moz-selection {
    background: var(--primary-orange);
    color: var(--background-black);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--background-black);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, var(--primary-orange), var(--primary-purple));
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, var(--primary-purple), var(--primary-orange));
}